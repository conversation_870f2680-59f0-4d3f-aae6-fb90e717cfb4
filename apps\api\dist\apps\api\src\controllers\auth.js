"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProfile = exports.resetPassword = exports.requestPasswordReset = exports.resendEmailVerification = exports.verifyEmail = exports.logoutAll = exports.logout = exports.refreshToken = exports.login = exports.register = void 0;
const database_1 = require("@freela/database");
const auth_1 = require("../utils/auth");
const logger_1 = require("../utils/logger");
const error_1 = require("../middleware/error");
const redis_1 = require("../utils/redis");
/**
 * User registration
 */
const register = (0, error_1.asyncHandler)(async (req, res) => {
    const { email, password, firstName, lastName, phone, role, language = 'ar', acceptTerms, } = req.body;
    // Check if terms are accepted
    if (!acceptTerms) {
        throw error_1.createError.badRequest('You must accept the terms and conditions', 'TERMS_NOT_ACCEPTED');
    }
    // Check if user already exists
    const existingUser = await database_1.prisma.user.findFirst({
        where: {
            OR: [
                { email: email.toLowerCase() },
                ...(phone ? [{ phone }] : []),
            ],
        },
    });
    if (existingUser) {
        if (existingUser.email === email.toLowerCase()) {
            throw error_1.createError.conflict('Email already registered', 'EMAIL_EXISTS');
        }
        if (existingUser.phone === phone) {
            throw error_1.createError.conflict('Phone number already registered', 'PHONE_EXISTS');
        }
    }
    // Hash password
    const passwordHash = await auth_1.passwordUtils.hash(password);
    // Generate verification token
    const emailVerificationToken = auth_1.authUtils.generateSecureToken();
    // Create user
    const user = await database_1.prisma.user.create({
        data: {
            email: email.toLowerCase(),
            firstName,
            lastName,
            phone,
            role: role.toUpperCase(),
            language,
            passwordHash,
            emailVerificationToken,
            status: 'PENDING_VERIFICATION',
        },
        select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            role: true,
            status: true,
            language: true,
            createdAt: true,
        },
    });
    // Store verification token in Redis with 24-hour expiry
    await redis_1.redis.set(`email_verification:${emailVerificationToken}`, user.id, 24 * 60 * 60 // 24 hours
    );
    // Log user registration
    (0, logger_1.logUserAction)('user_registered', user.id, {
        email: user.email,
        role: user.role,
        language: user.language,
    });
    // TODO: Send verification email
    // await emailService.sendVerificationEmail(user.email, emailVerificationToken);
    res.status(201).json({
        success: true,
        message: 'Registration successful. Please check your email for verification.',
        data: {
            user,
            verificationRequired: true,
        },
    });
});
exports.register = register;
/**
 * User login
 */
const login = (0, error_1.asyncHandler)(async (req, res) => {
    const { email, password, rememberMe = false } = req.body;
    // Find user by email
    const user = await database_1.prisma.user.findUnique({
        where: { email: email.toLowerCase() },
        select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            role: true,
            status: true,
            emailVerified: true,
            phoneVerified: true,
            passwordHash: true,
            lastLoginAt: true,
        },
    });
    if (!user) {
        (0, logger_1.logSecurityEvent)('login_attempt_invalid_email', { email }, req);
        throw error_1.createError.unauthorized('Invalid email or password', 'INVALID_CREDENTIALS');
    }
    // Verify password
    const isPasswordValid = await auth_1.passwordUtils.verify(password, user.passwordHash);
    if (!isPasswordValid) {
        (0, logger_1.logSecurityEvent)('login_attempt_invalid_password', { email, userId: user.id }, req);
        throw error_1.createError.unauthorized('Invalid email or password', 'INVALID_CREDENTIALS');
    }
    // Check user status
    if (user.status === 'SUSPENDED') {
        (0, logger_1.logSecurityEvent)('login_attempt_suspended_user', { userId: user.id }, req);
        throw error_1.createError.forbidden('Account is suspended', 'ACCOUNT_SUSPENDED');
    }
    if (user.status === 'INACTIVE') {
        (0, logger_1.logSecurityEvent)('login_attempt_inactive_user', { userId: user.id }, req);
        throw error_1.createError.forbidden('Account is inactive', 'ACCOUNT_INACTIVE');
    }
    // Create session and generate tokens
    const tokens = await auth_1.sessionUtils.createSession(user.id, user.email, user.role, {
        firstName: user.firstName,
        lastName: user.lastName,
        emailVerified: user.emailVerified,
        phoneVerified: user.phoneVerified,
        rememberMe,
    });
    // Update last login time
    await database_1.prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() },
    });
    // Log successful login
    (0, logger_1.logUserAction)('user_logged_in', user.id, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        rememberMe,
    });
    // Remove password hash from response
    const { passwordHash, ...userWithoutPassword } = user;
    res.json({
        success: true,
        message: 'Login successful',
        data: {
            user: userWithoutPassword,
            tokens,
        },
    });
});
exports.login = login;
/**
 * Refresh access token
 */
const refreshToken = (0, error_1.asyncHandler)(async (req, res) => {
    const { refreshToken } = req.body;
    if (!refreshToken) {
        throw error_1.createError.badRequest('Refresh token is required', 'REFRESH_TOKEN_REQUIRED');
    }
    const tokens = await auth_1.sessionUtils.refreshTokens(refreshToken);
    if (!tokens) {
        (0, logger_1.logSecurityEvent)('invalid_refresh_token', { token: refreshToken.substring(0, 20) + '...' }, req);
        throw error_1.createError.unauthorized('Invalid or expired refresh token', 'INVALID_REFRESH_TOKEN');
    }
    res.json({
        success: true,
        message: 'Tokens refreshed successfully',
        data: { tokens },
    });
});
exports.refreshToken = refreshToken;
/**
 * User logout
 */
const logout = (0, error_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw error_1.createError.unauthorized('Authentication required', 'AUTH_REQUIRED');
    }
    // Invalidate session
    await auth_1.sessionUtils.invalidateSession(req.user.sessionId);
    // Log logout
    (0, logger_1.logUserAction)('user_logged_out', req.user.id, {
        sessionId: req.user.sessionId,
    });
    res.json({
        success: true,
        message: 'Logout successful',
    });
});
exports.logout = logout;
/**
 * Logout from all devices
 */
const logoutAll = (0, error_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw error_1.createError.unauthorized('Authentication required', 'AUTH_REQUIRED');
    }
    // Invalidate all user sessions
    await auth_1.sessionUtils.invalidateAllUserSessions(req.user.id);
    // Log logout from all devices
    (0, logger_1.logUserAction)('user_logged_out_all', req.user.id);
    res.json({
        success: true,
        message: 'Logged out from all devices successfully',
    });
});
exports.logoutAll = logoutAll;
/**
 * Verify email
 */
const verifyEmail = (0, error_1.asyncHandler)(async (req, res) => {
    const { token } = req.body;
    if (!token) {
        throw error_1.createError.badRequest('Verification token is required', 'TOKEN_REQUIRED');
    }
    // Get user ID from Redis
    const userId = await redis_1.redis.get(`email_verification:${token}`);
    if (!userId) {
        throw error_1.createError.badRequest('Invalid or expired verification token', 'INVALID_TOKEN');
    }
    // Update user email verification status
    const user = await database_1.prisma.user.update({
        where: { id: userId },
        data: {
            emailVerified: true,
            status: 'ACTIVE',
            emailVerificationToken: null,
        },
        select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            emailVerified: true,
        },
    });
    // Remove verification token from Redis
    await redis_1.redis.del(`email_verification:${token}`);
    // Log email verification
    (0, logger_1.logUserAction)('email_verified', user.id);
    res.json({
        success: true,
        message: 'Email verified successfully',
        data: { user },
    });
});
exports.verifyEmail = verifyEmail;
/**
 * Resend email verification
 */
const resendEmailVerification = (0, error_1.asyncHandler)(async (req, res) => {
    const { email } = req.body;
    const user = await database_1.prisma.user.findUnique({
        where: { email: email.toLowerCase() },
        select: {
            id: true,
            email: true,
            emailVerified: true,
            status: true,
        },
    });
    if (!user) {
        throw error_1.createError.notFound('User not found', 'USER_NOT_FOUND');
    }
    if (user.emailVerified) {
        throw error_1.createError.badRequest('Email is already verified', 'EMAIL_ALREADY_VERIFIED');
    }
    // Generate new verification token
    const emailVerificationToken = auth_1.authUtils.generateSecureToken();
    // Update user with new token
    await database_1.prisma.user.update({
        where: { id: user.id },
        data: { emailVerificationToken },
    });
    // Store verification token in Redis
    await redis_1.redis.set(`email_verification:${emailVerificationToken}`, user.id, 24 * 60 * 60 // 24 hours
    );
    // TODO: Send verification email
    // await emailService.sendVerificationEmail(user.email, emailVerificationToken);
    // Log resend verification
    (0, logger_1.logUserAction)('email_verification_resent', user.id);
    res.json({
        success: true,
        message: 'Verification email sent successfully',
    });
});
exports.resendEmailVerification = resendEmailVerification;
/**
 * Request password reset
 */
const requestPasswordReset = (0, error_1.asyncHandler)(async (req, res) => {
    const { email } = req.body;
    const user = await database_1.prisma.user.findUnique({
        where: { email: email.toLowerCase() },
        select: {
            id: true,
            email: true,
            firstName: true,
        },
    });
    // Always return success to prevent email enumeration
    if (!user) {
        return res.json({
            success: true,
            message: 'If the email exists, a password reset link has been sent',
        });
    }
    // Generate reset token
    const resetToken = auth_1.authUtils.generateSecureToken();
    // Store reset token in Redis with 1-hour expiry
    await redis_1.redis.set(`password_reset:${resetToken}`, user.id, 60 * 60 // 1 hour
    );
    // TODO: Send password reset email
    // await emailService.sendPasswordResetEmail(user.email, resetToken);
    // Log password reset request
    (0, logger_1.logUserAction)('password_reset_requested', user.id);
    res.json({
        success: true,
        message: 'If the email exists, a password reset link has been sent',
    });
});
exports.requestPasswordReset = requestPasswordReset;
/**
 * Reset password
 */
const resetPassword = (0, error_1.asyncHandler)(async (req, res) => {
    const { token, newPassword } = req.body;
    if (!token || !newPassword) {
        throw error_1.createError.badRequest('Token and new password are required', 'MISSING_FIELDS');
    }
    // Validate password strength
    const passwordValidation = auth_1.authUtils.validatePasswordStrength(newPassword);
    if (!passwordValidation.isValid) {
        throw error_1.createError.badRequest('Password does not meet requirements', 'WEAK_PASSWORD', {
            errors: passwordValidation.errors,
        });
    }
    // Get user ID from Redis
    const userId = await redis_1.redis.get(`password_reset:${token}`);
    if (!userId) {
        throw error_1.createError.badRequest('Invalid or expired reset token', 'INVALID_TOKEN');
    }
    // Hash new password
    const passwordHash = await auth_1.passwordUtils.hash(newPassword);
    // Update user password
    await database_1.prisma.user.update({
        where: { id: userId },
        data: { passwordHash },
    });
    // Remove reset token from Redis
    await redis_1.redis.del(`password_reset:${token}`);
    // Invalidate all user sessions
    await auth_1.sessionUtils.invalidateAllUserSessions(userId);
    // Log password reset
    (0, logger_1.logUserAction)('password_reset_completed', userId);
    res.json({
        success: true,
        message: 'Password reset successfully',
    });
});
exports.resetPassword = resetPassword;
/**
 * Get current user profile
 */
const getProfile = (0, error_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw error_1.createError.unauthorized('Authentication required', 'AUTH_REQUIRED');
    }
    const user = await database_1.prisma.user.findUnique({
        where: { id: req.user.id },
        select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            phone: true,
            role: true,
            status: true,
            language: true,
            avatar: true,
            location: true,
            emailVerified: true,
            phoneVerified: true,
            lastLoginAt: true,
            createdAt: true,
            updatedAt: true,
        },
    });
    if (!user) {
        throw error_1.createError.notFound('User not found', 'USER_NOT_FOUND');
    }
    res.json({
        success: true,
        data: { user },
    });
});
exports.getProfile = getProfile;
//# sourceMappingURL=auth.js.map