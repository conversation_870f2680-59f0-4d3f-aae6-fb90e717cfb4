"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateAudioFile = exports.validateVideoFile = exports.validateDocumentFile = exports.validateImageFile = exports.validateFileType = exports.validateExpiryDate = exports.validateCVV = exports.validateCreditCard = exports.validateSyrianPhone = exports.validateEnglishText = exports.validateArabicText = exports.validateSlug = exports.validateUrl = exports.validatePhone = exports.validatePassword = exports.validateEmail = exports.fileUploadSchema = exports.reviewSchema = exports.messageSchema = exports.bookingSchema = exports.serviceSchema = exports.userProfileSchema = exports.userLoginSchema = exports.userRegistrationSchema = exports.slugSchema = exports.urlSchema = exports.phoneSchema = exports.passwordSchema = exports.emailSchema = void 0;
const zod_1 = require("zod");
// Common validation schemas
exports.emailSchema = zod_1.z.string().email('Invalid email address');
exports.passwordSchema = zod_1.z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character');
exports.phoneSchema = zod_1.z
    .string()
    .regex(/^(\+963|0)?[0-9]{8,9}$/, 'Invalid Syrian phone number');
exports.urlSchema = zod_1.z.string().url('Invalid URL');
exports.slugSchema = zod_1.z
    .string()
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens')
    .min(3, 'Slug must be at least 3 characters')
    .max(50, 'Slug must be at most 50 characters');
// User validation schemas
exports.userRegistrationSchema = zod_1.z.object({
    email: exports.emailSchema,
    password: exports.passwordSchema,
    firstName: zod_1.z.string().min(2, 'First name must be at least 2 characters'),
    lastName: zod_1.z.string().min(2, 'Last name must be at least 2 characters'),
    phone: exports.phoneSchema.optional(),
    role: zod_1.z.enum(['CLIENT', 'EXPERT', 'client', 'expert']).transform((val) => val.toUpperCase()),
    language: zod_1.z.enum(['ar', 'en']).default('ar'),
    acceptTerms: zod_1.z.boolean().refine((val) => val === true, 'You must accept the terms'),
});
exports.userLoginSchema = zod_1.z.object({
    email: exports.emailSchema,
    password: zod_1.z.string().min(1, 'Password is required'),
    rememberMe: zod_1.z.boolean().optional(),
});
exports.userProfileSchema = zod_1.z.object({
    firstName: zod_1.z.string().min(2, 'First name must be at least 2 characters'),
    lastName: zod_1.z.string().min(2, 'Last name must be at least 2 characters'),
    phone: exports.phoneSchema.optional(),
    bio: zod_1.z.string().max(500, 'Bio must be at most 500 characters').optional(),
    website: exports.urlSchema.optional(),
    location: zod_1.z.object({
        governorate: zod_1.z.string(),
        city: zod_1.z.string(),
        district: zod_1.z.string().optional(),
    }).optional(),
});
// Service validation schemas
exports.serviceSchema = zod_1.z.object({
    title: zod_1.z.object({
        ar: zod_1.z.string().min(10, 'Arabic title must be at least 10 characters'),
        en: zod_1.z.string().min(10, 'English title must be at least 10 characters').optional(),
    }),
    description: zod_1.z.object({
        ar: zod_1.z.string().min(50, 'Arabic description must be at least 50 characters'),
        en: zod_1.z.string().min(50, 'English description must be at least 50 characters').optional(),
    }),
    categoryId: zod_1.z.string().min(1, 'Category is required'),
    tags: zod_1.z.array(zod_1.z.string()).min(1, 'At least one tag is required').max(10, 'Maximum 10 tags allowed'),
    pricing: zod_1.z.object({
        type: zod_1.z.enum(['fixed', 'hourly', 'package', 'custom']),
        basePrice: zod_1.z.number().min(5, 'Minimum price is $5'),
        currency: zod_1.z.enum(['USD', 'SYP']).default('USD'),
    }),
    deliveryTime: zod_1.z.number().min(1, 'Delivery time must be at least 1 day').max(365, 'Maximum delivery time is 365 days'),
    revisions: zod_1.z.number().min(0, 'Revisions cannot be negative').max(10, 'Maximum 10 revisions'),
});
// Booking validation schemas
exports.bookingSchema = zod_1.z.object({
    serviceId: zod_1.z.string().min(1, 'Service is required'),
    packageId: zod_1.z.string().optional(),
    requirements: zod_1.z.array(zod_1.z.object({
        questionId: zod_1.z.string(),
        answer: zod_1.z.union([zod_1.z.string(), zod_1.z.array(zod_1.z.string())]),
    })),
    customInstructions: zod_1.z.string().max(1000, 'Instructions must be at most 1000 characters').optional(),
    preferredDeliveryDate: zod_1.z.date().optional(),
});
// Message validation schemas
exports.messageSchema = zod_1.z.object({
    content: zod_1.z.string().min(1, 'Message cannot be empty').max(2000, 'Message must be at most 2000 characters'),
    type: zod_1.z.enum(['text', 'file', 'image']).default('text'),
});
// Review validation schemas
exports.reviewSchema = zod_1.z.object({
    rating: zod_1.z.number().min(1, 'Rating must be at least 1').max(5, 'Rating must be at most 5'),
    title: zod_1.z.string().max(100, 'Title must be at most 100 characters').optional(),
    comment: zod_1.z.string().max(1000, 'Comment must be at most 1000 characters').optional(),
    wouldRecommend: zod_1.z.boolean(),
});
// File upload validation
exports.fileUploadSchema = zod_1.z.object({
    filename: zod_1.z.string().min(1, 'Filename is required'),
    mimeType: zod_1.z.string().min(1, 'MIME type is required'),
    size: zod_1.z.number().max(10 * 1024 * 1024, 'File size must be less than 10MB'),
});
// Validation helper functions
const validateEmail = (email) => {
    return exports.emailSchema.safeParse(email).success;
};
exports.validateEmail = validateEmail;
const validatePassword = (password) => {
    return exports.passwordSchema.safeParse(password).success;
};
exports.validatePassword = validatePassword;
const validatePhone = (phone) => {
    return exports.phoneSchema.safeParse(phone).success;
};
exports.validatePhone = validatePhone;
const validateUrl = (url) => {
    return exports.urlSchema.safeParse(url).success;
};
exports.validateUrl = validateUrl;
const validateSlug = (slug) => {
    return exports.slugSchema.safeParse(slug).success;
};
exports.validateSlug = validateSlug;
// Arabic text validation
const validateArabicText = (text) => {
    const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
    return arabicRegex.test(text);
};
exports.validateArabicText = validateArabicText;
// English text validation
const validateEnglishText = (text) => {
    const englishRegex = /^[a-zA-Z\s.,!?'"()-]+$/;
    return englishRegex.test(text);
};
exports.validateEnglishText = validateEnglishText;
// Syrian phone number validation
const validateSyrianPhone = (phone) => {
    const syrianPhoneRegex = /^(\+963|0)?[0-9]{8,9}$/;
    return syrianPhoneRegex.test(phone);
};
exports.validateSyrianPhone = validateSyrianPhone;
// Credit card validation
const validateCreditCard = (cardNumber) => {
    const cleaned = cardNumber.replace(/\s/g, '');
    const cardRegex = /^[0-9]{13,19}$/;
    if (!cardRegex.test(cleaned)) {
        return false;
    }
    // Luhn algorithm
    let sum = 0;
    let isEven = false;
    for (let i = cleaned.length - 1; i >= 0; i--) {
        let digit = parseInt(cleaned[i]);
        if (isEven) {
            digit *= 2;
            if (digit > 9) {
                digit -= 9;
            }
        }
        sum += digit;
        isEven = !isEven;
    }
    return sum % 10 === 0;
};
exports.validateCreditCard = validateCreditCard;
// CVV validation
const validateCVV = (cvv, cardType) => {
    if (cardType === 'amex') {
        return /^[0-9]{4}$/.test(cvv);
    }
    return /^[0-9]{3}$/.test(cvv);
};
exports.validateCVV = validateCVV;
// Expiry date validation
const validateExpiryDate = (month, year) => {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;
    const expMonth = parseInt(month);
    const expYear = parseInt(year);
    if (expMonth < 1 || expMonth > 12) {
        return false;
    }
    if (expYear < currentYear || (expYear === currentYear && expMonth < currentMonth)) {
        return false;
    }
    return true;
};
exports.validateExpiryDate = validateExpiryDate;
// File type validation
const validateFileType = (filename, allowedTypes) => {
    const extension = filename.split('.').pop()?.toLowerCase();
    return extension ? allowedTypes.includes(extension) : false;
};
exports.validateFileType = validateFileType;
// Image file validation
const validateImageFile = (filename) => {
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    return (0, exports.validateFileType)(filename, imageTypes);
};
exports.validateImageFile = validateImageFile;
// Document file validation
const validateDocumentFile = (filename) => {
    const documentTypes = ['pdf', 'doc', 'docx', 'txt', 'rtf'];
    return (0, exports.validateFileType)(filename, documentTypes);
};
exports.validateDocumentFile = validateDocumentFile;
// Video file validation
const validateVideoFile = (filename) => {
    const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
    return (0, exports.validateFileType)(filename, videoTypes);
};
exports.validateVideoFile = validateVideoFile;
// Audio file validation
const validateAudioFile = (filename) => {
    const audioTypes = ['mp3', 'wav', 'ogg', 'aac', 'flac'];
    return (0, exports.validateFileType)(filename, audioTypes);
};
exports.validateAudioFile = validateAudioFile;
// All exports are already handled by individual export statements above
//# sourceMappingURL=validation.js.map