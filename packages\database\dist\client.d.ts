import { PrismaClient, Prisma } from '@prisma/client';
type PrismaClientType = import('@prisma/client').PrismaClient;
declare const getPrismaInstance: () => PrismaClientType;
declare const prismaInstance: PrismaClient<Prisma.PrismaClientOptions, never, import(".prisma/client/runtime/library").DefaultArgs>;
declare const connectDatabase: () => Promise<void>;
declare const disconnectDatabase: () => Promise<void>;
declare const checkDatabaseHealth: () => Promise<boolean>;
/**
 * Wrapper for Prisma transactions.
 * @template T
 * @param {(tx: any) => Promise<T>} callback
 * @returns {Promise<T>}
 */
declare const withTransaction: <T>(callback: (tx: any) => Promise<T>) => Promise<T>;
export { prismaInstance as prisma, connectDatabase, disconnectDatabase, checkDatabaseHealth, withTransaction, getPrismaInstance, Prisma, PrismaClient, };
export default prismaInstance;
//# sourceMappingURL=client.d.ts.map