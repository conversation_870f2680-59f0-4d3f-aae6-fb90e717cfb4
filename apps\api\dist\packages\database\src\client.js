"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrismaClient = exports.Prisma = exports.getPrismaInstance = exports.withTransaction = exports.checkDatabaseHealth = exports.disconnectDatabase = exports.connectDatabase = exports.prisma = void 0;
const client_1 = require("@prisma/client");
Object.defineProperty(exports, "PrismaClient", { enumerable: true, get: function () { return client_1.PrismaClient; } });
Object.defineProperty(exports, "Prisma", { enumerable: true, get: function () { return client_1.Prisma; } });
let prisma;
// This is a singleton pattern to ensure we only have one instance of Prisma Client.
const getPrismaInstance = () => {
    if (prisma) {
        return prisma;
    }
    const newPrismaInstance = new client_1.PrismaClient({
        log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
        errorFormat: 'pretty',
    });
    // In development, use a global variable to preserve the client across hot reloads.
    if (process.env.NODE_ENV !== 'production') {
        if (!globalThis.__prisma) {
            globalThis.__prisma = newPrismaInstance;
        }
        prisma = globalThis.__prisma;
    }
    else {
        prisma = newPrismaInstance;
    }
    return prisma; // Non-null assertion since we just created it
};
exports.getPrismaInstance = getPrismaInstance;
// Immediately get the instance to be used by the app
const prismaInstance = getPrismaInstance();
exports.prisma = prismaInstance;
// Graceful shutdown logic
const setupGracefulShutdown = (client) => {
    let isShuttingDown = false;
    const shutdown = async (signal) => {
        if (isShuttingDown)
            return;
        isShuttingDown = true;
        console.log(`Received ${signal}. Disconnecting database...`);
        await client.$disconnect();
        console.log('Database disconnected.');
        process.exit(0);
    };
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
};
setupGracefulShutdown(prismaInstance);
// Database connection utilities with timeout and graceful failure
const connectDatabase = async () => {
    const client = getPrismaInstance();
    try {
        // Race connection against a timeout
        await Promise.race([
            client.$connect(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Database connection timed out after 10 seconds')), 10000)),
        ]);
        console.log('✅ Database connected successfully');
    }
    catch (error) {
        if (error instanceof Error) {
            console.error('❌ Database connection failed:', error.message);
        }
        else {
            console.error('❌ An unexpected error occurred during database connection:', error);
        }
        console.warn('⚠️ Server is starting without a database connection. Some features will be unavailable.');
        // Do not re-throw; allow the application to start in a degraded state.
        return; // Explicitly return to avoid any potential re-throw
    }
};
exports.connectDatabase = connectDatabase;
const disconnectDatabase = async () => {
    const client = getPrismaInstance();
    try {
        await client.$disconnect();
        console.log('✅ Database disconnected successfully');
    }
    catch (error) {
        if (error instanceof Error) {
            console.error('❌ Database disconnection failed:', error.message);
        }
        else {
            console.error('❌ An unexpected error occurred during database disconnection:', error);
        }
        // In a disconnect scenario, we should probably throw to indicate a problem.
        throw error;
    }
};
exports.disconnectDatabase = disconnectDatabase;
// Health check for the database connection
const checkDatabaseHealth = async () => {
    const client = getPrismaInstance();
    try {
        await client.$queryRaw `SELECT 1`;
        return true;
    }
    catch (error) {
        // Don't spam logs on health checks
        return false;
    }
};
exports.checkDatabaseHealth = checkDatabaseHealth;
/**
 * Wrapper for Prisma transactions.
 * @template T
 * @param {(tx: any) => Promise<T>} callback
 * @returns {Promise<T>}
 */
const withTransaction = async (callback) => {
    const client = getPrismaInstance();
    return await client.$transaction(callback);
};
exports.withTransaction = withTransaction;
exports.default = prismaInstance;
//# sourceMappingURL=client.js.map