{
  message: 'Test log',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:24:38'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:51:18'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:51:18'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:205:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 05:51:22'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:52:10'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:52:10'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:205:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 05:52:14'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:52:19'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:52:19'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 05:52:23'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:52:57'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:52:57'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 05:53:01'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:10:55'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:10:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 13:10:59'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:11:16'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:11:16'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 13:11:20'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:13:49'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:13:49'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 13:13:53'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:41'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:41'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:209:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-10 13:14:46'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:47'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:47'
}
